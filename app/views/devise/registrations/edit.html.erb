<h2 class="text-3xl font-bold text-primary">Edit <%= resource_name.to_s.humanize %></h2>

<%= form_for(resource, as: resource_name, url: user_registration_path(resource_name), html: { method: :put, multipart: true }) do |f| %>
  <%= render "devise/shared/error_messages", resource: resource %>
  
  <div class="mt-4" data-controller="image-preview">
    <%= image_tag resource.avatar_thumbnail, data: { image_preview_target: "preview" } %>
    
    <div class="mt-4">
      <%= f.file_field :avatar, class: 'file-input file-input-ghost', data: { action: "change->image-preview#show" } %>
    </div>
  </div>

  <div class="mt-4">
    <%= f.label :email, class: "block text-sm font-medium text-gray-700 mb-1"%><br />
    <%= f.email_field :email, autofocus: true, autocomplete: "email", class: "input input-primary" %>
  </div>

  <% if devise_mapping.confirmable? && resource.pending_reconfirmation? %>
    <div>Currently waiting confirmation for: <%= resource.unconfirmed_email %></div>
  <% end %>

  <div class="mt-4">
    <%= f.label :password, class: "block text-sm font-medium text-gray-700 mb-1" %> <i>(leave blank if you don't want to change it)</i><br />
    <%= f.password_field :password, autocomplete: "new-password", class: "input input-primary"%>
    <% if @minimum_password_length %>
      <br />
      <em><%= @minimum_password_length %> characters minimum</em>
    <% end %>
  </div>

  <div class="mt-4">
    <%= f.label :password_confirmation, class: "block text-sm font-medium text-gray-700 mb-1" %>
    <%= f.password_field :password_confirmation, autocomplete: "new-password", class: "input input-primary" %>
  </div>

  <div class="mt-4">
    <%= f.label :current_password, class: "block text-sm font-medium text-gray-700 mb-1" %> <i>(we need your current password to confirm your changes)</i><br />
    <%= f.password_field :current_password, autocomplete: "current-password", class: "input input-primary" %>
  </div>

  <div class="mt-4">
    <%= f.submit "Update" , class: "btn btn-primary" %>
  </div>
<% end %>

<div class="mt-4">Unhappy? <%= button_to "Cancel my account", user_registration_path(resource_name), data: { confirm: "Are you sure?", turbo_confirm: "Are you sure?" }, method: :delete, class: "btn btn-error"%></div>

<%= link_to "Back", :back , class: "btn btn-info mt-4"%>
