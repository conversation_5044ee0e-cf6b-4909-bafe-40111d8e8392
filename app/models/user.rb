class User < ApplicationRecord
  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :validatable
  validates :role, inclusion: { in: %w[admin editor viewer], message: "%{value} is not a valid role" }
  after_initialize :set_default_role, if: :new_record?
  has_one_attached :avatar

  def set_default_role
    self.role ||= "viewer"
  end

  def admin?
    role == "admin"
  end

  def editor?
    role == "editor"
  end

  def viewer?
    role == "viewer"
  end

  def avatar_thumbnail
    if avatar.attached?
      avatar.variant(resize: "150x150!").processed
    else
      "/default-profile.jpg"
    end
  end
end
