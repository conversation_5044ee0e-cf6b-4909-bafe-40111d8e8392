class ApplicationController < ActionController::Base
  before_action :configure_permitted_parameters, if: :devise_controller?
  # Only allow modern browsers supporting webp images, web push, badges, import maps, CSS nesting, and CSS :has.
  allow_browser versions: :modern

  # Switch locale based on params[:locale] https://guides.rubyonrails.org/i18n.html#managing-the-locale-across-requests
  around_action :switch_locale

  # Use different layout for devise controllers
  layout :layout_by_resource

  def switch_locale(&action)
    locale = params[:locale] || detect_locale || I18n.default_locale
    I18n.with_locale(locale, &action)
  end

  # Setting the Locale from URL Params
  def default_url_options
    { locale: I18n.locale }
  end

  # Detect locale from browser
  def detect_locale
     preferred_languages = request.env["HTTP_ACCEPT_LANGUAGE"]&.scan(/\w{2}/)&.map(&:downcase)
     available = I18n.available_locales.map(&:to_s)
     detected_locale = preferred_languages&.find { |lang| available.include?(lang) }
     detected_locale&.to_sym
  end

  def layout_by_resource
    if devise_controller? && action_name == "new"
      "auth"
    elsif devise_controller? && action_name == "edit"
      "dashboard"
    else
      "application"
    end
  end

  def after_sign_in_path_for(resource)
    if resource.viewer?
      root_path(locale: detect_locale)
    else
      dashboard_root_path(locale: detect_locale)
    end
  end

  def after_sign_up_path_for(resource)
    if resource.viewer?
      root_path(locale: detect_locale)
    else
      dashboard_root_path(locale: detect_locale)
    end
  end

  rescue_from CanCan::AccessDenied do |exception|
    respond_to do |format|
      format.json { render nothing: true, status: :forbidden }
      format.html { redirect_to access_denied_path(locale: detect_locale, message: exception.message) }
      format.js   { render nothing: true, status: :forbidden }
    end
  end

  protected

  def configure_permitted_parameters
    devise_parameter_sanitizer.permit(:sign_up, keys: [ :avatar ])
    devise_parameter_sanitizer.permit(:account_update, keys: [ :avatar ])
  end
end
