// This file is responsible for registering all your Stimulus controllers.
// It's compatible with esbuild and does not use `require.context`.

import { application } from "./application"

import AlertController from "./alert_controller.js"
application.register("alert", AlertController)

import LanguageController from "./language_controller.js"
application.register("language", LanguageController)

import ThemeController from "./theme_controller.js"
application.register("theme", ThemeController)

import TiptapController from "./tiptap_controller.js"
application.register("tiptap", TiptapController)

import HelloController from "./hello_controller.js"
application.register("hello", HelloController)

import ImagePreviewController from "./image_preview_controller.js"
application.register("image-preview", ImagePreviewController)
